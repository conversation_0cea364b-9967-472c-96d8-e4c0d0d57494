// src/dataset.rs

use anyhow::Result;
use polars::prelude::*;
use std::fs;
use std::path::Path;

/// Processes raw 1-minute k-line data into a model-ready dataset of returns.
///
/// # Arguments
/// * `raw_data_dir` - Path to the root directory containing symbol subdirectories (e.g., "raw_data/").
/// * `output_path` - Path to save the final processed Parquet file.
/// * `symbols` - A slice of symbol strings to process (e.g., &["BTCUSDT", "ETHUSDT"]).
/// * `interval` - The time interval to resample to (e.g., "1h", "15m", "1d").
pub fn create_dataset(
    raw_data_dir: &Path,
    output_path: &Path,
    symbols: &[&str],
    interval: &str,
) -> Result<()> {
    println!("Starting dataset creation...");
    let mut processed_frames: Vec<LazyFrame> = Vec::new();

    // 1. Process each symbol individually
    for symbol in symbols {
        let symbol_dir = raw_data_dir.join(symbol);
        if !symbol_dir.exists() {
            eprintln!("Warning: Directory not found for symbol {}, skipping.", symbol);
            continue;
        }

        println!("Processing symbol: {}", symbol);

        // Scan all Parquet files in the symbol's directory
        let raw_df = LazyFrame::scan_parquet(
            symbol_dir.join("*.parquet").to_str().unwrap(),
            Default::default(),
        )?;

        // 2. Resample, calculate returns, and select columns
        let processed_lf = raw_df
            // Convert Unix ms timestamp to Polars Datetime
            .with_column(
                col("open_time")
                    .cast(DataType::Datetime(TimeUnit::Milliseconds, None))
                    .alias("open_time"),
            )
            // Resample to the desired interval (e.g., 1 hour)
            .group_by_dynamic(
                col("open_time"),
                [],
                DynamicGroupOptions {
                    every: Duration::parse(interval),
                    period: Duration::parse(interval),
                    offset: Duration::new(0),
                    label: Label::Left,
                    closed_window: ClosedWindow::Left,
                    start_by: StartBy::WindowBound,
                },
            )
            .agg([
                // For the hourly price, we take the last `close` price in that hour window
                col("close").last().alias("close"),
            ])
            .sort(["open_time"], Default::default())
            // 3. Calculate percentage return
            .with_column(
                ((col("close") - col("close").shift(1)) / col("close").shift(1))
                    .alias(&format!("{}_return", symbol)),
            )
            // Keep only the timestamp and the new return column
            .select([col("open_time"), col(&format!("{}_return", symbol))]);

        processed_frames.push(processed_lf);
    }

    if processed_frames.is_empty() {
        anyhow::bail!("No data processed. Check symbol directories and names.");
    }

    // 4. Join all individual frames into one large DataFrame
    println!("Joining data for all symbols...");
    let mut final_lf = processed_frames.remove(0);
    for lf in processed_frames {
        final_lf = final_lf.join(
            lf,
            [col("open_time")],
            [col("open_time")],
            JoinArgs::new(JoinType::Outer),
        );
    }

    // 5. Clean the joined data
    let final_lf = final_lf
        .sort(["open_time"], Default::default())
        // Forward fill to propagate last known values into missing slots
        .with_columns(all().forward_fill(None))
        // Fill any remaining nulls at the beginning of the series with 0.0
        .with_columns(all().fill_null(lit(0.0f64)))
        // Drop the timestamp column as it's not needed for the model's input matrix
        .drop_columns(["open_time"]);


    println!("Collecting final DataFrame...");
    let mut final_df = final_lf.collect()?;

    // 6. Save the final dataset
    println!(
        "Saving processed dataset with shape {:?} to {}",
        final_df.shape(),
        output_path.display()
    );
    let mut file = fs::File::create(output_path)?;
    ParquetWriter::new(&mut file)
        .with_compression(ParquetCompression::Snappy)
        .finish(&mut final_df)?;

    println!("✅ Dataset creation complete.");
    Ok(())
}