
/// Single download attempt for any time period (day or month)
fn download_data_attempt(
    client: &reqwest::blocking::Client,
    url: &str,
    period_str: &str,
) -> Result<Option<DataFrame>> {
    // Make the HTTP request
    let response = client.get(url).send()
        .map_err(|e| anyhow::anyhow!("Failed to send request to {}: {}", url, e))?;

    // Check if the data exists for this day (Binance returns 404 if not)
    if response.status() == reqwest::StatusCode::NOT_FOUND {
        return Ok(None);
    }

    // Check for other HTTP errors
    if !response.status().is_success() {
        return Err(anyhow::anyhow!("HTTP error {}: {}", response.status(), url));
    }

    // Get the response bytes
    let zip_data = response.bytes()
        .map_err(|e| anyhow::anyhow!("Failed to read response body from {}: {}", url, e))?;

    // Unzip and parse the data
    let mut archive = ::zip::ZipArchive::new(std::io::Cursor::new(zip_data))
        .map_err(|e| anyhow::anyhow!("Failed to read ZIP archive for {}: {}", period_str, e))?;

    if archive.is_empty() {
        return Err(anyhow::anyhow!("Empty zip file for {}", period_str));
    }

    let mut file_in_zip = archive.by_index(0)
        .map_err(|e| anyhow::anyhow!("Failed to extract file from ZIP for {}: {}", period_str, e))?;

    let mut csv_content = String::new();
    file_in_zip.read_to_string(&mut csv_content)
        .map_err(|e| anyhow::anyhow!("Failed to read CSV content for {}: {}", period_str, e))?;

    // Parse CSV content with Polars - keep it lazy and minimal processing
    // Klines data structure: Open time|Open|High|Low|Close|Volume|Close time|Quote asset volume|Number of trades|Taker buy base asset volume|Taker buy quote asset volume|Ignore
    let df = CsvReadOptions::default()
        .with_has_header(false)
        .into_reader_with_file_handle(std::io::Cursor::new(csv_content.as_bytes()))
        .finish()?
        .lazy()
        .with_columns([
            col("column_1").alias("open_time").cast(DataType::Int64),
            col("column_2").alias("open").cast(DataType::Float64),
            col("column_3").alias("high").cast(DataType::Float64),
            col("column_4").alias("low").cast(DataType::Float64),
            col("column_5").alias("close").cast(DataType::Float64),
            col("column_6").alias("volume").cast(DataType::Float64),
            col("column_7").alias("close_time").cast(DataType::Int64),
            col("column_8").alias("quote_asset_volume").cast(DataType::Float64),
            col("column_9").alias("number_of_trades").cast(DataType::UInt32),
            col("column_10").alias("taker_buy_base_asset_volume").cast(DataType::Float64),
            col("column_11").alias("taker_buy_quote_asset_volume").cast(DataType::Float64),
            col("column_12").alias("ignore").cast(DataType::UInt8),
        ])
        .select([
            col("open_time"),
            col("open"),
            col("high"),
            col("low"),
            col("close"),
            col("volume"),
            col("close_time"),
            col("quote_asset_volume"),
            col("number_of_trades"),
            col("taker_buy_base_asset_volume"),
            col("taker_buy_quote_asset_volume"),
        ])
        .collect()?;

    println!("Successfully downloaded and parsed data for {} ({} rows)",
             period_str, df.height());

    Ok(Some(df))
}

/// Save yearly data to a single Parquet file (combines multiple months)
fn save_yearly_parquet(data_dir: &Path, year: i32, dataframes: Vec<DataFrame>) -> Result<()> {
    // Create directory structure: data_dir/
    fs::create_dir_all(data_dir)?;

    // Save as YYYY.parquet
    let file_path = data_dir.join(format!("{}.parquet", year));

    if file_path.exists() {
        println!("Yearly file already exists: {}", file_path.display());
        return Ok(());
    }

    // Combine all dataframes for the year
    let combined_df = if dataframes.len() == 1 {
        dataframes.into_iter().next().unwrap()
    } else {
        // Concatenate all dataframes using polars concat function
        let lazy_frames: Vec<LazyFrame> = dataframes.into_iter()
            .map(|df| df.lazy())
            .collect();

        // Use polars concat function to combine all lazy frames
        let combined = concat(lazy_frames, Default::default())?;

        // Sort by open_time to ensure chronological order
        combined
            .sort(["open_time"], SortMultipleOptions::default())
            .collect()?
    };

    // Write the combined data
    let mut file = File::create(&file_path)?;
    ParquetWriter::new(&mut file)
        .with_compression(ParquetCompression::Snappy)
        .finish(&mut combined_df.clone())?;

    println!("✅ Saved yearly data: {} ({} rows)", file_path.display(), combined_df.height());
    Ok(())
}